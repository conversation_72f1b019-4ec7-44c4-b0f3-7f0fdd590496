<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>OpenLayers XYZ瓦片加载示例 - 乐山市沙湾区</title>

    <!-- 引入OpenLayers CSS和JS -->
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/ol@8.2.0/ol.css"
      type="text/css"
    />
    <script src="https://cdn.jsdelivr.net/npm/ol@8.2.0/dist/ol.js"></script>

    <style>
      html,
      body {
        width: 100%;
        height: 100%;
        padding: 0;
        margin: 0;
        overflow: hidden;
        font-family: Arial, sans-serif;
      }

      #mapContainer {
        width: 100%;
        height: 100%;
      }

      /* //地图配色 */
    .baseLayerClass {
        filter: grayscale(65%) sepia(50%) invert(87%) saturate(365%);
 
    }

      .info-panel {
        position: absolute;
        top: 10px;
        left: 10px;
        background: rgba(255, 255, 255, 0.9);
        padding: 15px;
        border-radius: 5px;
        font-size: 12px;
        z-index: 1000;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        max-width: 250px;
      }

      .layer-control {
        position: absolute;
        top: 10px;
        right: 10px;
        background: rgba(255, 255, 255, 0.9);
        padding: 10px;
        border-radius: 5px;
        font-size: 12px;
        z-index: 1000;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
      }

      .layer-control label {
        display: block;
        margin: 5px 0;
        cursor: pointer;
      }
    </style>
  </head>

  <body>
    <div id="mapContainer"></div>

    <div class="info-panel">
      <strong>乐山市沙湾区地图</strong><br />
      <small>基于OpenLayers的XYZ瓦片加载</small><br /><br />
      <div id="mousePosition"></div>
    </div>

    <div class="layer-control">
      <strong>图层控制</strong><br />
      <label
        ><input type="radio" name="baseLayer" value="vector" checked />
        矢量底图</label
      >
      <label
        ><input type="radio" name="baseLayer" value="image" /> 影像底图</label
      >
      <hr style="margin: 10px 0" />
      <label
        ><input type="checkbox" id="labelsToggle" checked /> 地图标注</label
      >
      <hr style="margin: 10px 0" />
      <label
        ><input type="checkbox" id="pipePointsToggle" checked />
        管线点要素</label
      >
      <label
        ><input type="checkbox" id="pipeLinesToggle" checked />
        管线线要素</label
      >
      <label
        ><input type="checkbox" id="mvtToggle" checked /> MVT矢量瓦片</label
      >
    </div>

    <script>
      /**
       * 乐山市沙湾区XYZ瓦片服务配置
       * @type {Object}
       */
      const TILE_SERVICES = {
        // 矢量底图服务
        vectorBase:
          'http://*************:8077/bcserver/services/swq_tdt_digital/rest/xyz/{z}/{x}/{y}/tile.png',
        // 地图标注服务
        labels:
          'http://*************:8077/bcserver/services/swq_tdt_label/rest/xyz/{z}/{x}/{y}/tile.png',
        // 影像底图服务
        imageBase:
          'http://*************:8077/bcserver/services/swq_tdt_image/rest/xyz/{z}/{x}/{y}/tile.png',
        // 地形底图服务
        terrainBase:
          'http://*************:8077/bcserver/services/swq_tdt_terrain/rest/xyz/{z}/{x}/{y}/tile.png',
        // 管线MVT矢量瓦片服务
        lineMvtTiles:
          'http://*************:8078/api/analyse/gs/ln/mvt/{z}/{x}/{y}.mvt',
        // 管点MVT矢量瓦片服务
        // pointMvtTiles:
        //   'http://************:8077/swq-pipe-bcserver/analyse/gs/pt/mvt/{z}/{x}/{y}.mvt',
        // 管点MVT矢量瓦片服务
        pointMvtTiles:
          'http://*************:8078/api/analyse/gs/pt/mvt/{z}/{x}/{y}.mvt',
        //爆管分析服务地址
        pipeBreakAnalysis:
          'http://*************:8077/bcserver/lsgwfiles/rest/network/explosion',
      };


      /**
       * 乐山市沙湾区地理中心坐标（Web Mercator投影）
       * @type {Array<number>}
       */
      const SHASHAN_CENTER = ol.proj.fromLonLat([103.55, 29.41]);

      try {
        /**
         * 创建矢量底图图层
         * @type {ol.layer.Tile}
         */
        const vectorBaseLayer = new ol.layer.Tile({
          // className: 'baseLayerClass',
          source: new ol.source.XYZ({
            url: TILE_SERVICES.vectorBase,
            maxZoom: 18,
            attributions: '乐山市沙湾区矢量底图服务',
          }),
          title: 'vectorBase',
        });

        /**
         * 创建影像底图图层
         * @type {ol.layer.Tile}
         */
        const imageBaseLayer = new ol.layer.Tile({
          source: new ol.source.XYZ({
            url: TILE_SERVICES.imageBase,
            maxZoom: 18,
            attributions: '乐山市沙湾区影像底图服务',
          }),
          title: 'imageBase',
          visible: false,
        });

        /**
         * 创建标注图层
         * @type {ol.layer.Tile}
         */
        const labelsLayer = new ol.layer.Tile({
          source: new ol.source.XYZ({
            url: TILE_SERVICES.labels,
            maxZoom: 18,
            attributions: '乐山市沙湾区地图标注服务',
          }),
          title: 'labels',
        });

      

        /**
         * 创建MVT矢量瓦片图层
         * @type {ol.layer.VectorTile}
         */
        const mvtLayer = new ol.layer.VectorTile({
          source: new ol.source.VectorTile({
            format: new ol.format.MVT(),
            url: TILE_SERVICES.lineMvtTiles,
            maxZoom: 18,
          }),
          style: new ol.style.Style({
            stroke: new ol.style.Stroke({
              color: '#ff6600',
              width: 3,
            }),
          }),
          title: 'mvtTiles',
        });
        /**
         * 创建MVT矢量瓦片图层
         * @type {ol.layer.VectorTile}
         */
        const mvtLayer2 = new ol.layer.VectorTile({
          source: new ol.source.VectorTile({
            format: new ol.format.MVT(),
            url: TILE_SERVICES.pointMvtTiles,
            maxZoom: 18,
          }),
          style: new ol.style.Style({
            image: new ol.style.Circle({
              radius: 5,
              fill: new ol.style.Fill({
                color: '#ff3300',
              }),
              stroke: new ol.style.Stroke({
                color: '#000000',
                width: 2,
              }),
            }),
          }),
          title: 'mvtTiles',
        });

        /**
         * 初始化OpenLayers地图
         * @type {ol.Map}
         */
        const map = new ol.Map({
          target: 'mapContainer',
          layers: [
            vectorBaseLayer,
            //imageBaseLayer,
            labelsLayer,
            mvtLayer,
            mvtLayer2,
          ],
          view: new ol.View({
            center: SHASHAN_CENTER,
            zoom: 13,
            projection: 'EPSG:3857',
          }),
          // controls: ol.control.defaults.defaults().extend([
          //   new ol.control.ScaleLine({
          //     units: 'metric',
          //   }),
          //   new ol.control.MousePosition({
          //     coordinateFormat: ol.coordinate.createStringXY(4),
          //     projection: 'EPSG:4326',
          //     target: document.getElementById('mousePosition'),
          //     undefinedHTML: '&nbsp;',
          //   }),
          // ]),
        });

        /**
         * 底图切换功能
         */
        const baseLayerRadios = document.querySelectorAll(
          'input[name="baseLayer"]'
        );
        baseLayerRadios.forEach((radio) => {
          radio.addEventListener('change', function () {
            if (this.value === 'vector') {
              vectorBaseLayer.setVisible(true);
              imageBaseLayer.setVisible(false);
            } else if (this.value === 'image') {
              vectorBaseLayer.setVisible(false);
              imageBaseLayer.setVisible(true);
            }
          });
        });

        /**
         * 标注图层开关功能
         */
        const labelsToggle = document.getElementById('labelsToggle');
        labelsToggle.addEventListener('change', function () {
          labelsLayer.setVisible(this.checked);
        });

        /**
         * 管线点要素图层开关功能
         */
        const pipePointsToggle = document.getElementById('pipePointsToggle');
        pipePointsToggle.addEventListener('change', function () {
          pipePointsLayer.setVisible(this.checked);
        });

        /**
         * 管线线要素图层开关功能
         */
        const pipeLinesToggle = document.getElementById('pipeLinesToggle');
        pipeLinesToggle.addEventListener('change', function () {
          pipeLinesLayer.setVisible(this.checked);
        });

        /**
         * MVT矢量瓦片图层开关功能
         */
        const mvtToggle = document.getElementById('mvtToggle');
        mvtToggle.addEventListener('change', function () {
          mvtLayer.setVisible(this.checked);
        });

        /**
         * 添加点击事件获取坐标和要素信息
         */
        map.on('click', function (event) {
          const coordinate = ol.proj.toLonLat(event.coordinate);
          console.log('点击坐标:', coordinate);

          // 检查是否点击了要素
          const feature = map.forEachFeatureAtPixel(
            event.pixel,
            function (feature, layer) {
              return { feature: feature, layer: layer };
            }
          );

          if (feature) {
            const properties = feature.feature.getProperties();
            let layerType = '未知图层';

            // 判断图层类型
            if (feature.layer === mvtLayer) {
              layerType = 'MVT矢量瓦片';
            } else if (feature.layer === pipePointsLayer) {
              layerType = '管线点要素';
            } else if (feature.layer === pipeLinesLayer) {
              layerType = '管线线要素';
            }

            let info = `要素信息 (${layerType}):\n`;
            Object.keys(properties).forEach((key) => {
              if (
                key !== 'geometry' &&
                properties[key] !== null &&
                properties[key] !== undefined
              ) {
                info += `${key}: ${properties[key]}\n`;
              }
            });
            alert(info);
          } else {
            console.log('未点击到任何要素');
          }
        });

        // console.log('OpenLayers XYZ瓦片加载完成 - 乐山市沙湾区');
      } catch (error) {
        console.error('OpenLayers初始化失败:', error);
        document.getElementById('mapContainer').innerHTML =
          '<div style="color: red; padding: 20px; text-align: center; display: flex; align-items: center; justify-content: center; height: 100%;">' +
          '<div>' +
          '<h3>地图加载失败</h3>' +
          '<p>错误信息: ' +
          error.message +
          '</p>' +
          '<p>请检查网络连接和瓦片服务是否可用</p>' +
          '</div>' +
          '</div>';
      }
    </script>
  </body>
</html>
