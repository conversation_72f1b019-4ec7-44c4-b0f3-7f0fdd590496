<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Cesium XYZ瓦片加载示例 - 乐山市沙湾区 (Cesium 1.99)</title>

    <!-- 引入Cesium CSS和JS - 版本 1.99 -->
    <script src="https://cesium.com/downloads/cesiumjs/releases/1.99/Build/Cesium/Cesium.js"></script>
    <link
      href="https://cesium.com/downloads/cesiumjs/releases/1.99/Build/Cesium/Widgets/widgets.css"
      rel="stylesheet"
    />

    <style>
      html,
      body {
        width: 100%;
        height: 100%;
        padding: 0;
        margin: 0;
        overflow: hidden;
      }

      #cesiumContainer {
        width: 100%;
        height: 100%;
      }

      .cesium-viewer-timelineContainer,
      .cesium-viewer-animationContainer,
      .cesium-viewer-bottom {
        display: none;
      }

      .cesium-viewer-fullscreenContainer {
        display: none;
      }
    </style>
  </head>

  <body>
    <div id="cesiumContainer"></div>

    <script>
      /**
       * 乐山市沙湾区XYZ瓦片服务配置
       * @type {Object}
       */
      const TILE_SERVICES = {
        // 矢量底图服务
        vectorBase:
          'http://*************:8077/bcserver/services/swq_tdt_digital/rest/xyz/{z}/{x}/{y}/tile.png',
        // 地图标注服务
        labels:
          'http://*************:8077/bcserver/services/swq_tdt_label/rest/xyz/{z}/{x}/{y}/tile.png',
        // 影像底图服务
        imageBase:
          'http://*************:8077/bcserver/services/swq_tdt_image/rest/xyz/{z}/{x}/{y}/tile.png',
      };

      const reailtyUrls = [
        {
          name: '倾斜摄影1',
          type: 'reality',
          visible: 1,
          url: 'https://oss.lshywater.cn/_TILES_1_/tileset.json',
        },
        {
          name: '倾斜摄影2',
          type: 'reality',
          visible: 1,
          url: 'https://oss.lshywater.cn/_TILES_2_/tileset.json',
        },
        {
          name: '倾斜摄影3',
          type: 'reality',
          visible: 1,
          url: 'https://oss.lshywater.cn/_TILES_3_/tileset.json',
        },
      ];

      /**
       * WFS管线服务配置
       * @type {Object}
       */
      const WFS_SERVICES = {
        // 管线点要素服务
        pipePoints:
          'http://**************:8079/bcserver/swq_pipe/swq_pipe/wfs?service=WFS&request=GetFeature&version=1.0&typename=swq_pipe:SWQ_PT&outputFormat=application/json',
        // 管线线要素服务
        pipeLines:
          'http://**************:8079/bcserver/swq_pipe/swq_pipe/wfs?service=WFS&request=GetFeature&version=1.0&typename=swq_pipe:SWQ_LN&outputFormat=application/json',
      };

      /**
       * 乐山市沙湾区地理中心坐标
       * @type {Object}
       */
      const SHASHAN_CENTER = {
        longitude: 103.55,
        latitude: 29.41,
        height: 50000,
      };

      try {
        // 设置Cesium Ion访问令牌（使用示例令牌，实际使用时应替换为有效令牌）
        Cesium.Ion.defaultAccessToken =
          'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiIxMTc2YzIxOC03YmM2LTRjZDktYTJjZi0zODM1NWYwYzVhOTIiLCJpZCI6NTY3MTAsImlhdCI6MTY5NTYyMjc5M30.xQMA57OLuh7YlgaJFHgWK_XgscxU7heGJYiFXl2aQSQ';
        Cesium.Resource.createIfNeeded({
          url: 'https://example.com/your-image.jpg',
          crossOrigin: 'anonymous',
        });
        /**
         * 初始化Cesium Viewer
         * @type {Cesium.Viewer}
         */
        const viewer = new Cesium.Viewer('cesiumContainer', {
          homeButton: false,
          sceneModePicker: false,
          baseLayerPicker: false, // 影像切换
          animation: false, // 是否显示动画控件
          timeline: false, // 是否显示时间线控件，和animation是一组
          infoBox: false, // 是否显示点击要素之后显示的信息
          selectionIndicator: false, // 要素选中框
          geocoder: false, // 是否显示地名查找控件
          fullscreenButton: false,
          shouldAnimate: false,
          navigationHelpButton: false, // 是否显示帮助信息控件
        });

        /**
         * 创建矢量底图图层
         * @type {Cesium.UrlTemplateImageryProvider}
         */
        const vectorBaseLayer = new Cesium.UrlTemplateImageryProvider({
          url: TILE_SERVICES.vectorBase,
          maximumLevel: 18,
          credit: '乐山市沙湾区矢量底图服务',
          crossOrigin: 'anonymous',
        });

        /**
         * 创建标注图层
         * @type {Cesium.UrlTemplateImageryProvider}
         */
        const labelsLayer = new Cesium.UrlTemplateImageryProvider({
          url: TILE_SERVICES.labels,
          maximumLevel: 18,
          credit: '乐山市沙湾区地图标注服务',
          crossOrigin: 'anonymous',
        });

        // 添加矢量底图图层
        viewer.imageryLayers.addImageryProvider(vectorBaseLayer);

        // 添加标注图层
        viewer.imageryLayers.addImageryProvider(labelsLayer);

        /**
         * 设置地图视图到乐山市沙湾区中心
         */
        viewer.camera.setView({
          destination: Cesium.Cartesian3.fromDegrees(
            SHASHAN_CENTER.longitude,
            SHASHAN_CENTER.latitude,
            SHASHAN_CENTER.height
          ),
        });

        /**
         * 创建倾斜摄影透明度自定义着色器
         * 使用 CustomShader 实现 90% 透明度效果（alpha = 0.1）
         * 兼容 Cesium 1.99 版本
         * @type {Cesium.CustomShader}
         */
        const realityTransparencyShader = new Cesium.CustomShader({
          // 启用透明渲染模式，确保 alpha 值被正确处理
          translucencyMode: Cesium.CustomShaderTranslucencyMode.TRANSLUCENT,
          fragmentShaderText: `
            void fragmentMain(FragmentInput fsInput, inout czm_modelMaterial material) {
              // 保持原有的纹理和颜色，只修改透明度为 0.1（90% 透明）
              material.alpha = 0.1;
            }
          `,
        });

        /**
         * 加载倾斜摄影 3D Tiles 列表到场景
         * @param {Array<{name:string,type:string,visible:number|boolean,url:string}>} urlConfigs - 倾斜摄影配置
         * @param {Cesium.Viewer} viewer - Cesium Viewer 实例
         * @returns {Promise<Cesium.Cesium3DTileset[]>} 返回已添加到场景的 tileset 列表
         * 使用JSDoc注释 | 采用 async/await | 完善错误处理 | 兼容 Cesium 1.99
         */
        async function loadRealityTilesets(urlConfigs, viewer) {
          /** @type {Cesium.Cesium3DTileset[]} */
          const tilesets = [];
          try {
            if (!Array.isArray(urlConfigs)) {
              console.warn('loadRealityTilesets: 非法的 urlConfigs 参数');
              return tilesets;
            }
            if (!viewer || !viewer.scene) {
              console.warn('loadRealityTilesets: Viewer 未初始化');
              return tilesets;
            }

            for (const cfg of urlConfigs) {
              if (!cfg || !cfg.url) {
                console.warn('跳过无效的倾斜摄影配置:', cfg);
                continue;
              }
              try {
                const tileset = await Cesium.Cesium3DTileset.fromUrl(cfg.url, {
                  maximumScreenSpaceError: 16,
                  preferLeaves: true,
                  // 应用透明度自定义着色器，实现 90% 透明效果
                  customShader: realityTransparencyShader,
                });
                viewer.scene.primitives.add(tileset);
                tileset.show = cfg.visible !== 0 && cfg.visible !== false;
                tilesets.push(tileset);

                // 在首个成功加载的 tileset 上飞行定位
                if (tilesets.length === 1) {
                  try {
                    await tileset.readyPromise; // 双重保障已就绪
                    const bs = tileset.boundingSphere;
                    if (bs) {
                      viewer.scene.camera.flyToBoundingSphere(bs, { duration: 1.5 });
                    }
                  } catch (locErr) {
                    console.warn('首个 tileset 定位失败，将忽略定位错误:', locErr);
                  }
                }
                console.log(`3D Tiles 已加载: ${cfg.name || cfg.url}`);
              } catch (oneErr) {
                console.error(`加载 3D Tiles 失败: ${cfg?.name || cfg?.url}`, oneErr);
              }
            }
          } catch (error) {
            console.error('loadRealityTilesets 执行失败:', error);
          }
          return tilesets;
        }

        // 加载倾斜摄影（不阻塞主流程）
        loadRealityTilesets(reailtyUrls, viewer)
          .then((ts) => { window.realityTilesets = ts; })
          .catch((e) => console.error('倾斜摄影批量加载失败:', e));


        /**
         * 加载WFS管线数据（使用Primitive集合优化性能）
         */
        async function loadWFSData() {
          try {
            // 创建高性能Primitive集合
            const pointCollection = viewer.scene.primitives.add(
              new Cesium.PointPrimitiveCollection()
            );
            const polylineCollection = viewer.scene.primitives.add(
              new Cesium.PolylineCollection()
            );

            // 加载管线点要素数据
            const pointsResponse = await fetch(WFS_SERVICES.pipePoints);
            const pointsGeoJson = await pointsResponse.json();

            // 处理点要素数据
            if (pointsGeoJson && pointsGeoJson.features) {
              pointsGeoJson.features.forEach((feature) => {
                if (feature.geometry && feature.geometry.type === 'Point') {
                  const coordinates = feature.geometry.coordinates;
                  const position = Cesium.Cartesian3.fromDegrees(
                    coordinates[0],
                    coordinates[1]
                  );

                  pointCollection.add({
                    position: position,
                    pixelSize: 8,
                    color: Cesium.Color.YELLOW,
                    outlineColor: Cesium.Color.BLACK,
                    outlineWidth: 2,
                    heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
                    // 存储属性信息供后续使用
                    id: feature.properties,
                  });
                }
              });
              console.log(
                `已加载 ${pointsGeoJson.features.length} 个管线点要素`
              );
            }

            // 加载管线线要素数据
            const linesResponse = await fetch(WFS_SERVICES.pipeLines);
            const linesGeoJson = await linesResponse.json();

            // 处理线要素数据
            if (linesGeoJson && linesGeoJson.features) {
              let lineStringCount = 0;
              let multiLineStringCount = 0;
              let polylineSegmentCount = 0;

              linesGeoJson.features.forEach((feature) => {
                if (feature.geometry) {
                  // 处理LineString类型
                  if (feature.geometry.type === 'LineString') {
                    lineStringCount++;
                    const coordinates = feature.geometry.coordinates;
                    const positions = coordinates.map((coord) =>
                      Cesium.Cartesian3.fromDegrees(coord[0], coord[1])
                    );

                    polylineCollection.add({
                      positions: positions,
                      width: 3,
                      material: Cesium.Material.fromType('Color', {
                        color: Cesium.Color.CYAN,
                      }),
                      // 注意：PolylineCollection不支持clampToGround属性
                      // 存储属性信息供后续使用
                      id: feature.properties,
                    });
                    polylineSegmentCount++;
                  }
                  // 处理MultiLineString类型
                  else if (feature.geometry.type === 'MultiLineString') {
                    multiLineStringCount++;
                    const coordinatesArray = feature.geometry.coordinates;

                    // 为MultiLineString中的每个LineString创建单独的polyline
                    coordinatesArray.forEach(
                      (lineCoordinates, segmentIndex) => {
                        const positions = lineCoordinates.map((coord) =>
                          Cesium.Cartesian3.fromDegrees(coord[0], coord[1])
                        );

                        polylineCollection.add({
                          positions: positions,
                          width: 3,
                          material: Cesium.Material.fromType('Color', {
                            color: Cesium.Color.CYAN,
                          }),
                          // 注意：PolylineCollection不支持clampToGround属性
                          // 存储属性信息供后续使用
                          id: {
                            ...feature.properties,
                            segmentIndex: segmentIndex, // 标记是第几个线段
                          },
                        });
                        polylineSegmentCount++;
                      }
                    );
                  }
                }
              });

              console.log(`线要素加载统计：`);
              console.log(`- LineString: ${lineStringCount} 个`);
              console.log(`- MultiLineString: ${multiLineStringCount} 个`);
              console.log(`- 总计渲染线段: ${polylineSegmentCount} 个`);
              console.log(
                `已加载 ${linesGeoJson.features.length} 个管线线要素（使用PolylineCollection）`
              );
            }

            console.log('WFS管线数据加载完成（使用Primitive集合优化）');
          } catch (error) {
            console.error('WFS数据加载失败:', error);
          }
        }

        // 调用WFS数据加载函数
        loadWFSData();
        // addTiandituLayerFilter('theme-dark', vectorBaseLayer, viewer);
        console.log('Cesium XYZ瓦片加载完成 - 乐山市沙湾区 (Cesium 1.99)');
      } catch (error) {
        console.error('Cesium初始化失败:', error);
        document.getElementById('cesiumContainer').innerHTML =
          '<div style="color: red; padding: 20px; text-align: center;">' +
          '<h3>地图加载失败</h3>' +
          '<p>错误信息: ' +
          error.message +
          '</p>' +
          '</div>';
      }
      // 在外部保存原始着色器代码的引用
      // let originalFragShaderSources = ref([]);
      // 添加颜色滤镜  主题
      function addTiandituLayerFilter(sideTheme, vectorBaseLayer, viewer) {
        if (sideTheme === 'theme-dark') {
          const filterParams = {
            bInvertColor: true, // 启用颜色反转
            bFilterColor: true, // 启用颜色过滤
            filterColor: '#0044aa', // 过滤颜色值
          };
          vectorBaseLayer.brightness = 0.6; // 亮度降低
          vectorBaseLayer.contrast = 1.8; // 对比度增强
          vectorBaseLayer.gamma = 0.3; // 伽马值调整
          vectorBaseLayer.hue = 1; // 色相保持
          vectorBaseLayer.saturation = 0; // 饱和度归零
          filterLayer(filterParams, viewer);
        } else {
          // // 亮色主题：恢复原始着色器
          // if(originalFragShaderSources.value.length > 0) {
          //     viewer.scene.globe._surfaceShaderSet.baseFragmentShaderSource.sources = originalFragShaderSources.value
          // }
        }
      }

      function filterLayer(options, viewer) {
        const { bInvertColor, bFilterColor, filterColor } = options;
        const color = new Cesium.Color.fromCssColorString(filterColor);
        // 提取RGB分量并转换为0-255整数
        const filterRGB = [
          Math.round(color.red * 255),
          Math.round(color.green * 255),
          Math.round(color.blue * 255),
        ];
        let fragShader =
          viewer.scene.globe._surfaceShaderSet.baseFragmentShaderSource.sources;

        // originalFragShaderSources.value = JSON.parse(JSON.stringify(fragShader))
        for (let i = 0; i < fragShader.length; i++) {
          // 定位要修改的代码位置（饱和度计算后）
          const strS =
            'color = czm_saturation(color, textureSaturation);\n#endif\n';
          let strT =
            'color = czm_saturation(color, textureSaturation);\n#endif\n';
          if (bInvertColor) {
            strT += `
                color.r = 1.0 - color.r;
                color.g = 1.0 - color.g;
                color.b = 1.0 - color.b;
            `;
          }
          if (bFilterColor) {
            strT += `
                color.r = color.r * ${filterRGB[0]}.0/255.0;
                color.g = color.g * ${filterRGB[1]}.0/255.0;
                color.b = color.b * ${filterRGB[2]}.0/255.0;
            `;
          }
          fragShader[i] = fragShader[i].replace(strS, strT);
        }
      }
    </script>
  </body>
</html>
