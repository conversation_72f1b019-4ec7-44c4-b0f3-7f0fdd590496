<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Leaflet XYZ瓦片加载示例 - 乐山市沙湾区</title>
    
    <!-- 引入Leaflet CSS和JS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" 
          integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" 
          crossorigin=""/>
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" 
            integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" 
            crossorigin=""></script>
    
    <!-- 引入Leaflet.VectorGrid插件用于MVT支持 -->
    <script src="https://unpkg.com/leaflet.vectorgrid@latest/dist/Leaflet.VectorGrid.bundled.js"></script>
    
    <style>
        html, body {
            width: 100%;
            height: 100%;
            padding: 0;
            margin: 0;
            overflow: hidden;
        }
        
        #mapContainer {
            width: 100%;
            height: 100%;
        }
        
        .info-panel {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(255, 255, 255, 0.9);
            padding: 10px;
            border-radius: 5px;
            font-family: Arial, sans-serif;
            font-size: 12px;
            z-index: 1000;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }
    </style>
</head>

<body>
    <div id="mapContainer"></div>
    <div class="info-panel">
        <strong>乐山市沙湾区地图</strong><br>
        矢量底图 + 标注图层
    </div>
    
    <script>
        /**
         * 乐山市沙湾区XYZ瓦片服务配置
         * @type {Object}
         */
        const TILE_SERVICES = {
            // 矢量底图服务
            vectorBase: 'http://**************:8079/bcserver/services/swq_tdt_digital/rest/xyz/{z}/{x}/{y}/tile.png',
            // 地图标注服务  
            labels: 'http://**************:8079/bcserver/services/swq_tdt_label/rest/xyz/{z}/{x}/{y}/tile.png',
            // 影像底图服务
            imageBase: 'http://**************:8079/bcserver/services/swq_tdt_image/rest/xyz/{z}/{x}/{y}/tile.png',
            // MVT矢量瓦片服务
            mvtTiles: 'http://************:9111/api/analyse/gs/ln/mvt/{z}/{x}/{y}.mvt'
        };

        /**
         * WFS管线服务配置
         * @type {Object}
         */
        const WFS_SERVICES = {
            // 管线点要素服务
            pipePoints: 'http://**************:8079/bcserver/swq_pipe/swq_pipe/wfs?service=WFS&request=GetFeature&version=1.0&typename=swq_pipe:SWQ_PT&outputFormat=application/json',
            // 管线线要素服务
            pipeLines: 'http://**************:8079/bcserver/swq_pipe/swq_pipe/wfs?service=WFS&request=GetFeature&version=1.0&typename=swq_pipe:SWQ_LN&outputFormat=application/json'
        };
        
        /**
         * 乐山市沙湾区地理中心坐标
         * @type {Array<number>}
         */
        const SHASHAN_CENTER = [29.41, 103.55]; // [纬度, 经度]

        try {
            /**
             * 初始化Leaflet地图
             * @type {L.Map}
             */
            const map = L.map('mapContainer').setView(SHASHAN_CENTER, 13);

            /**
             * 创建矢量底图图层
             * @type {L.TileLayer}
             */
            const vectorBaseLayer = L.tileLayer(TILE_SERVICES.vectorBase, {
                maxZoom: 18,
                attribution: '乐山市沙湾区矢量底图服务'
            });

            /**
             * 创建标注图层
             * @type {L.TileLayer}
             */
            const labelsLayer = L.tileLayer(TILE_SERVICES.labels, {
                maxZoom: 18,
                attribution: '乐山市沙湾区地图标注服务'
            });

            // 添加矢量底图图层到地图
            vectorBaseLayer.addTo(map);
            
            // 添加标注图层到地图
            labelsLayer.addTo(map);

            /**
             * 图层控制器配置
             * @type {Object}
             */
            const baseMaps = {
                "矢量底图": vectorBaseLayer,
                "影像底图": L.tileLayer(TILE_SERVICES.imageBase, {
                    maxZoom: 18,
                    attribution: '乐山市沙湾区影像底图服务'
                })
            };

            const overlayMaps = {
                "地图标注": labelsLayer
            };

            // 添加图层控制器 - 稍后会添加管线图层
            const layerControl = L.control.layers(baseMaps, overlayMaps).addTo(map);

            /**
             * 更新图层控制器，添加管线图层
             */
            function updateLayerControl() {
                if (pipePointsLayer && pipeLinesLayer) {
                    layerControl.addOverlay(pipePointsLayer, "管线点要素");
                    layerControl.addOverlay(pipeLinesLayer, "管线线要素");
                }
                if (mvtLayer) {
                    layerControl.addOverlay(mvtLayer, "MVT矢量瓦片");
                }
            }

            /**
             * 添加比例尺控件
             */
            L.control.scale({
                metric: true,
                imperial: false
            }).addTo(map);

            /**
             * 管线图层组
             * @type {L.LayerGroup}
             */
            let pipePointsLayer = null;
            let pipeLinesLayer = null;
            let mvtLayer = null;

            /**
             * 创建MVT矢量瓦片图层
             */
            function createMVTLayer() {
                try {
                    mvtLayer = L.vectorGrid.protobuf(TILE_SERVICES.mvtTiles, {
                        vectorTileLayerStyles: {
                            // 为不同的图层设置样式 - 如果不知道具体图层名称，使用通用样式
                            'default': {
                                weight: 2,
                                color: '#ff6600',
                                opacity: 0.8,
                                fillColor: '#ff6600',
                                fillOpacity: 0.6,
                                fill: true
                            },
                            // 可能的图层名称样式配置
                            'lines': {
                                weight: 3,
                                color: '#ff6600',
                                opacity: 0.8
                            },
                            'points': {
                                radius: 5,
                                color: '#ff3300',
                                fillColor: '#ff3300',
                                fillOpacity: 0.7,
                                weight: 2
                            }
                        },
                        interactive: true,
                        maxZoom: 18,
                        getFeatureId: function(f) {
                            return f.properties.id || f.id;
                        }
                    });

                    // 添加点击事件
                    mvtLayer.on('click', function(e) {
                        if (e.layer && e.layer.properties) {
                            let popupContent = '<b>MVT矢量要素</b><br>';
                            Object.keys(e.layer.properties).forEach(key => {
                                if (e.layer.properties[key] !== null) {
                                    popupContent += `<b>${key}:</b> ${e.layer.properties[key]}<br>`;
                                }
                            });
                            L.popup()
                                .setLatLng(e.latlng)
                                .setContent(popupContent)
                                .openOn(map);
                        }
                    });

                    // 添加图层到地图
                    mvtLayer.addTo(map);
                    
                    console.log('MVT矢量瓦片图层创建完成');
                } catch (error) {
                    console.error('MVT图层创建失败:', error);
                }
            }

            /**
             * 加载WFS管线数据
             */
            async function loadWFSData() {
                try {
                    // 加载管线点要素
                    const pointsResponse = await fetch(WFS_SERVICES.pipePoints);
                    const pointsData = await pointsResponse.json();
                    
                    pipePointsLayer = L.geoJSON(pointsData, {
                        pointToLayer: function(feature, latlng) {
                            return L.circleMarker(latlng, {
                                radius: 6,
                                fillColor: "#ffff00",
                                color: "#000",
                                weight: 2,
                                opacity: 1,
                                fillOpacity: 0.8
                            });
                        },
                        onEachFeature: function(feature, layer) {
                            // 添加弹出框显示属性信息
                            if (feature.properties) {
                                let popupContent = '<b>管线点要素</b><br>';
                                Object.keys(feature.properties).forEach(key => {
                                    if (feature.properties[key] !== null) {
                                        popupContent += `<b>${key}:</b> ${feature.properties[key]}<br>`;
                                    }
                                });
                                layer.bindPopup(popupContent);
                            }
                        }
                    });

                    // 加载管线线要素
                    const linesResponse = await fetch(WFS_SERVICES.pipeLines);
                    const linesData = await linesResponse.json();
                    
                    pipeLinesLayer = L.geoJSON(linesData, {
                        style: function(feature) {
                            return {
                                color: "#00ffff",
                                weight: 3,
                                opacity: 0.8
                            };
                        },
                        onEachFeature: function(feature, layer) {
                            // 添加弹出框显示属性信息
                            if (feature.properties) {
                                let popupContent = '<b>管线线要素</b><br>';
                                Object.keys(feature.properties).forEach(key => {
                                    if (feature.properties[key] !== null) {
                                        popupContent += `<b>${key}:</b> ${feature.properties[key]}<br>`;
                                    }
                                });
                                layer.bindPopup(popupContent);
                            }
                        }
                    });

                    // 添加图层到地图
                    pipePointsLayer.addTo(map);
                    pipeLinesLayer.addTo(map);

                    // 更新图层控制器
                    updateLayerControl();

                    console.log('WFS管线数据加载完成');
                } catch (error) {
                    console.error('WFS数据加载失败:', error);
                }
            }

            // 调用WFS数据加载函数
            // loadWFSData();

            // 创建MVT图层
            createMVTLayer();

            // 确保图层控制器包含所有图层
            setTimeout(() => {
                updateLayerControl();
            }, 1000);

            console.log('Leaflet XYZ瓦片加载完成 - 乐山市沙湾区');

        } catch (error) {
            console.error('Leaflet初始化失败:', error);
            document.getElementById('mapContainer').innerHTML = 
                '<div style="color: red; padding: 20px; text-align: center; display: flex; align-items: center; justify-content: center; height: 100%;">' +
                '<div>' +
                '<h3>地图加载失败</h3>' +
                '<p>错误信息: ' + error.message + '</p>' +
                '</div>' +
                '</div>';
        }
    </script>
</body>
</html> 